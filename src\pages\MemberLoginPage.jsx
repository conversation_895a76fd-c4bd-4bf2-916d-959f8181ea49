import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { GlobalContext, showToast } from "../globalContext";
import { AuthContext } from "../authContext";
import MkdSDK from "../utils/MkdSDK";
import { ClipLoader } from "react-spinners";
import { getUserDetailsByIdAPI } from "Src/services/userService";
import StripeOnboardingModal from "Components/StripeOnboardingModal";

let sdk = new MkdSDK();

const MemberLoginPage = () => {
  const schema = yup
    .object({
      email: yup.string().email().required(),
      password: yup.string().required(),
    })
    .required();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);

  const projectParam = searchParams.get("project");

  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: GlobalDispatch, state } = React.useContext(GlobalContext);

  const [submitLoading, setSubmitLoading] = useState(false);
  const [showStripeModal, setShowStripeModal] = useState(false);
  const [stripeModalLoading, setStripeModalLoading] = useState(false);
  const [userDetailsForStripe, setUserDetailsForStripe] = useState(null);
  const [triggerStripeModal, setTriggerStripeModal] = useState(null);

  const redirect_uri = searchParams.get("redirect_uri");
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const SubscriptionType = localStorage.getItem("UserSubscription");

  // Debug: Monitor showStripeModal state changes
  React.useEffect(() => {
    console.log("🎭 showStripeModal state changed to:", showStripeModal);
  }, [showStripeModal]);

  // Handle Stripe modal trigger
  React.useEffect(() => {
    if (triggerStripeModal) {
      console.log("🚀 Triggering Stripe modal with data:", triggerStripeModal);
      setUserDetailsForStripe(triggerStripeModal);
      setShowStripeModal(true);
      setTriggerStripeModal(null); // Reset trigger
    }
  }, [triggerStripeModal]);

  // Helper function to check if user needs Stripe onboarding
  const checkStripeOnboarding = async (loginResult) => {
    try {
      const userId = loginResult.user_id;

      if (userId) {
        const userDetailsResult = await getUserDetailsByIdAPI(userId);

        if (!userDetailsResult.error) {
          const userDetails = userDetailsResult.model;

          // Check if user is a main member (not a sub-member)
          const isMainMember =
            !userDetails.main_user_details ||
            userDetails.main_user_details.is_self === true;

          // Only show Stripe modal if:
          // 1. User is a main member (not a sub-member)
          // 2. User doesn't have Stripe setup (has_stripe: false)
          // 3. User has a stripe onboard URL available
          console.log("🔍 Checking conditions:");
          console.log("- isMainMember:", isMainMember);
          console.log(
            "- has_stripe === false:",
            loginResult.has_stripe === false
          );
          console.log(
            "- stripe_onboard_url exists:",
            !!loginResult.stripe_onboard_url
          );

          if (
            isMainMember &&
            loginResult.has_stripe === false &&
            loginResult.stripe_onboard_url
          ) {
            console.log("✅ All conditions met - returning true");
            return { shouldShow: true, loginResult };
          } else {
            console.log("❌ Conditions not met, modal stays false");
          }
        }
      }
    } catch (error) {
      console.error(
        "Error checking user details for Stripe onboarding:",
        error
      );
    }

    return null;
  };

  // Handle Stripe setup button click
  const handleStripeSetup = async () => {
    if (!userDetailsForStripe?.stripe_onboard_url) {
      showToast(
        GlobalDispatch,
        "Stripe setup URL not available",
        4000,
        "error"
      );
      return;
    }

    setStripeModalLoading(true);

    // Redirect to Stripe onboarding
    window.location.href = userDetailsForStripe.stripe_onboard_url;
  };

  // Handle skip Stripe setup
  const handleSkipStripe = () => {
    setShowStripeModal(false);

    // Continue with normal login flow
    const sdk2 = new MkdSDK();
    sdk2
      .getCustomerStripeSubscription()
      .then((subscriptionResult) => {
        if (subscriptionResult.customer?.planId) {
          localStorage.setItem("is_plan", true);
        } else if (!subscriptionResult.customer?.planId) {
          localStorage.setItem("is_plan", false);
          navigate("/member/onboarding");
        } else {
          navigate(
            redirect_uri ??
              `${
                parseInt(SubscriptionType) === 2
                  ? "/member/projects"
                  : "/member/dashboard"
              }`
          );
        }
      })
      .catch((error) => {
        console.error("Error checking subscription:", error);
        navigate("/member/dashboard");
      });
  };

  React.useEffect(() => {
    const userId = localStorage.getItem("user");

    if (userId) {
      (async function () {
        try {
          const result = await getUserDetailsByIdAPI(userId);

          if (!result?.error) {
            localStorage.setItem("photo", result?.model?.photo);
            localStorage.setItem(
              "UserSubscription",
              result?.model?.subscription
            );
          }
        } catch (error) {}
      })();
    }
  }, []);

  const onSubmit = async (data) => {
    try {
      setSubmitLoading(true);
      const result = await sdk.login(data.email, data.password, "member");
      if (!result.error) {
        localStorage.setItem("UserSubscription", result?.subscription);
        localStorage.setItem("member_photo", result?.company_logo);

        localStorage.setItem("license_logo", result?.license_company_logo);
        localStorage.setItem("token", result?.token);

        dispatch({
          type: "LOGIN",
          payload: result,
        });
        localStorage.removeItem("memberTypeViewEdits");
        localStorage.removeItem("memberCompletedViewEdits");
        localStorage.removeItem("memberPendingViewEdits");

        showToast(GlobalDispatch, "Successfully Logged In", 4000, "success");

        // Check if user needs Stripe onboarding first
        const stripeCheck = await checkStripeOnboarding(result);
        console.log("🔄 stripeCheck returned:", stripeCheck);

        if (stripeCheck && stripeCheck.shouldShow) {
          console.log("🎯 FORCING modal to show - direct approach");
          setSubmitLoading(false);

          // Force immediate state update
          setTimeout(() => {
            console.log("🔥 Setting modal state in setTimeout");
            setUserDetailsForStripe(stripeCheck.loginResult);
            setShowStripeModal(true);
          }, 100);

          return; // Stop here and show the modal
        }

        console.log("➡️ Continuing with normal login flow...");

        const sdk2 = new MkdSDK();
        const subscriptionResult = await sdk2.getCustomerStripeSubscription();

        if (projectParam) {
          localStorage.setItem("projectClientId", "");
          localStorage.setItem("projectTeamName", "");
          localStorage.setItem("projectMixTypeId", "");
          localStorage.setItem("projectMixDateStart", "");
          localStorage.setItem("projectMixDateEnd", "");
          localStorage.setItem("projectPageSize", "");
          navigate(`/member/view-project/${projectParam}`);
        }
        if (subscriptionResult.customer?.planId) {
          localStorage.setItem("is_plan", true);
        } else if (!subscriptionResult.customer?.planId) {
          localStorage.setItem("is_plan", false);
          navigate("/member/onboarding");
        } else {
          navigate(
            redirect_uri ??
              `${
                parseInt(SubscriptionType) === 2
                  ? "/member/projects"
                  : "/member/dashboard"
              }`
          );
        }
      } else {
        setSubmitLoading(false);
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        }
      }
    } catch (error) {
      setSubmitLoading(false);

      showToast(GlobalDispatch, error.message, 4000, "error");
      setError("email", {
        type: "manual",
        message: error.response.data.message
          ? error.response.data.message
          : error.message,
      });
    }
  };

  console.log(showStripeModal);
  return (
    <>
      <StripeOnboardingModal
        isOpen={showStripeModal}
        onClose={() => setShowStripeModal(false)}
        onSetupPayment={handleStripeSetup}
        onSkip={handleSkipStripe}
        isLoading={stripeModalLoading}
      />

      <div className="flex h-full max-w-screen">
        <div className="flex justify-center items-center w-full min-h-screen rounded border shadow-default border-form-strokedark bg-boxdark dark:border-form-strokedark dark:bg-boxdark">
          <div className="flex flex-wrap items-center w-full">
            {/* Left Side - Image */}
            <div className="hidden w-full xl:block xl:w-1/2">
              <div className="py-17.5 px-26 text-center">
                <Link className="mb-5.5 inline-block" to="/">
                  <img
                    crossOrigin="anonymous"
                    src={
                      state.siteLogo ??
                      `${window.location.origin}/new/cheerEQ-2-Ed2.png`
                    }
                    className="h-auto w-[300px] dark:hidden"
                    alt="Logo"
                  />
                </Link>

                <p className="2xl:px-20">
                  Welcome back! Please sign in to access your account.
                </p>

                <span className="inline-block mt-15">
                  {/* You can add your login illustration SVG here */}
                </span>
              </div>
            </div>

            {/* Right Side - Login Form */}
            <div className="px-12 w-full border-form-strokedark xl:w-1/2 xl:border-l-2 dark:border-form-strokedark">
              <div className="sm:p-12.5 xl:p-17.5 w-full p-4">
                <h2 className="mb-9 text-2xl font-bold text-white sm:text-title-xl2 dark:text-white">
                  Sign In to Your Account
                </h2>

                <form onSubmit={handleSubmit(onSubmit)}>
                  <div className="mb-4">
                    <label className="mb-2.5 block font-medium text-white dark:text-white">
                      Email
                    </label>
                    <div className="relative">
                      <input
                        type="email"
                        placeholder="Enter your email"
                        {...register("email")}
                        className="py-4 pr-10 pl-6 w-full text-white rounded-lg border outline-none border-form-strokedark bg-form-input focus:border-primary focus-visible:shadow-none dark:border-form-strokedark dark:bg-form-input dark:text-white dark:focus:border-primary"
                      />
                      {errors.email && (
                        <span className="mt-1 text-sm text-red-500">
                          {errors.email.message}
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="mb-6">
                    <label className="mb-2.5 block font-medium text-white dark:text-white">
                      Password
                    </label>
                    <div className="relative">
                      <input
                        type="password"
                        placeholder="Enter your password"
                        {...register("password")}
                        className="py-4 pr-10 pl-6 w-full text-white rounded-lg border outline-none border-form-strokedark bg-form-input focus:border-primary focus-visible:shadow-none dark:border-form-strokedark dark:bg-form-input dark:text-white dark:focus:border-primary"
                      />
                      {errors.password && (
                        <span className="mt-1 text-sm text-red-500">
                          {errors.password.message}
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="mb-5">
                    <button
                      type="submit"
                      disabled={submitLoading}
                      className="p-4 w-full text-white rounded-lg border transition cursor-pointer border-primary bg-primary hover:bg-opacity-90 disabled:opacity-50"
                    >
                      {submitLoading ? (
                        <ClipLoader size={18} color="#fff" />
                      ) : (
                        "Sign In"
                      )}
                    </button>
                  </div>

                  <div className="mt-6 text-center">
                    <Link
                      to="/member/forgot"
                      className="text-primary hover:underline"
                    >
                      Forgot Password?
                    </Link>
                  </div>

                  <div className="mt-6 text-center">
                    <Link to="/" className="text-white hover:text-primary">
                      Back to Home
                    </Link>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default MemberLoginPage;
