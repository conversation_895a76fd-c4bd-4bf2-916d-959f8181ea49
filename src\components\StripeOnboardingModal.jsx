import React from "react";
import { Dialog, Transition } from "@headlessui/react";
import { Fragment } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

const StripeOnboardingModal = ({ 
  isOpen, 
  onClose, 
  onSetupPayment, 
  onSkip,
  isLoading = false 
}) => {
  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={() => {}}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-50" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-boxdark p-6 text-left align-middle shadow-xl transition-all border border-strokedark">
                <div className="flex items-center justify-center mb-4">
                  <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                    <FontAwesomeIcon
                      icon="fa-solid fa-credit-card"
                      className="text-2xl text-primary"
                    />
                  </div>
                </div>

                <Dialog.Title
                  as="h3"
                  className="text-lg font-medium leading-6 text-white text-center mb-2"
                >
                  Setup Payment System
                </Dialog.Title>

                <div className="mt-2 mb-6">
                  <p className="text-sm text-bodydark text-center">
                    To receive invoice subscription payments, you need to setup your payment system with Stripe. 
                    This will allow you to accept payments from clients for your services.
                  </p>
                </div>

                <div className="bg-boxdark-2 rounded-lg p-4 mb-6">
                  <h4 className="text-white font-medium mb-2">What you'll need:</h4>
                  <ul className="text-sm text-bodydark space-y-1">
                    <li>• Business information</li>
                    <li>• Bank account details</li>
                    <li>• Tax identification number</li>
                    <li>• Business verification documents</li>
                  </ul>
                </div>

                <div className="flex flex-col gap-3">
                  <button
                    type="button"
                    className="inline-flex justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/90 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:opacity-50"
                    onClick={onSetupPayment}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Setting up...
                      </>
                    ) : (
                      "Setup Payment System"
                    )}
                  </button>
                  
                  <button
                    type="button"
                    className="inline-flex justify-center rounded-md border border-strokedark bg-transparent px-4 py-2 text-sm font-medium text-bodydark hover:bg-boxdark-2 focus:outline-none focus-visible:ring-2 focus-visible:ring-strokedark focus-visible:ring-offset-2"
                    onClick={onSkip}
                    disabled={isLoading}
                  >
                    Skip for Now
                  </button>
                </div>

                <div className="mt-4 text-center">
                  <p className="text-xs text-bodydark2">
                    You can setup your payment system later from your dashboard settings.
                  </p>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default StripeOnboardingModal;
