import React from "react";
import { useNavigate } from "react-router-dom";
import { GlobalContext, showToast } from "Src/globalContext";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { CardDetailsModal } from "./MemberSubscriptionPage";

const stripePromise = loadStripe(
  "pk_test_51Ll5ukBgOlWo0lDUrBhA2W7EX2MwUH9AR5Y3KQoujf7PTQagZAJylWP1UOFbtH4UwxoufZbInwehQppWAq53kmNC00UIKSmebO"
);

const SubscriptionDetailsPage = ({
  selectedPlan,
  planDetails,
  onBack,
  onProceed,
  billingInterval,
  projectRange,
}) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [isProcessing, setIsProcessing] = React.useState(false);
  const navigate = useNavigate();

  const handleProceedToPayment = () => {
    setIsModalOpen(true);
  };

  const handleCardSubmit = async (result) => {
    if (result?.error) {
      showToast(globalDispatch, result?.error?.message, 4000, "error");
      return;
    }

    setIsProcessing(true);
    try {
      // Pass the token to the parent component
      await onProceed(result.token.id);
    } catch (error) {
      showToast(globalDispatch, error.message, 4000, "error");
    } finally {
      setIsProcessing(false);
      setIsModalOpen(false);
    }
  };

  // Get the pricing based on the selected plan, project range and billing interval
  const getPricing = () => {
    const planKey = {
      "The Portal": "portal",
      "The Studio": "studio",
      "Complete Suite": "complete",
    }[planDetails.name];

    const projectRanges = {
      "1-50": {
        portal: { monthly: 150, annual: 1500 },
        studio: { monthly: 150, annual: 1500 },
        complete: { monthly: 250, annual: 2500 },
      },
      "51-100": {
        portal: { monthly: 175, annual: 1750 },
        studio: { monthly: 175, annual: 1750 },
        complete: { monthly: 275, annual: 2750 },
      },
      "101-150": {
        portal: { monthly: 200, annual: 2000 },
        studio: { monthly: 200, annual: 2000 },
        complete: { monthly: 300, annual: 3000 },
      },
      "151-200": {
        portal: { monthly: 225, annual: 2250 },
        studio: { monthly: 225, annual: 2250 },
        complete: { monthly: 325, annual: 3250 },
      },
      "201+": {
        portal: { monthly: 250, annual: 2500 },
        studio: { monthly: 250, annual: 2500 },
        complete: { monthly: 350, annual: 3500 },
      },
    };

    return projectRanges[projectRange][planKey][
      billingInterval === "year" ? "annual" : "monthly"
    ];
  };

  const planFeatures = {
    "The Portal": [
      "Project Management",
      "Project Calendar",
      "Client Login Portal",
      "Digital 8-count sheets",
      "Automated Music Licenses",
      "Automated Reminder Emails",
      "Automated Music Surveys",
      "Project Edit Management",
      "8-Count Track Management",
      "Custom Email Domain",
    ],
    "The Studio": [
      "Automated Music Surveys",
      "Project Management",
      "Project Calendar",
      "Project Budget Review",
      "Automated Vocal Orders",
      "Excel Style Order View",
      "Automated Reminder Emails",
      "Company Logo Customization",
      "Custom Email Domain",
    ],
    "Complete Suite": [
      "Everything in The Portal",
      "Everything in The Studio",
      "Priority Support",
      "Dedicated Account Manager",
    ],
  };

  const addOnOptions = {
    "The Portal": [
      "Payment System Add-on ($399.99/month)",
      "Additional Members ($25/month per member)",
    ],
    "The Studio": ["Additional Members ($25/month per member)"],
    "Complete Suite": [
      "Payment System Add-on ($399.99/month)",
      "Additional Members ($25/month per member)",
    ],
  };

  return (
    <div className="mx-auto max-w-4xl">
      <div className="mb-8 rounded-lg bg-boxdark p-6">
        <div className="mb-6 flex items-center justify-between">
          <h2 className="text-2xl font-bold text-white">
            Subscription Details
          </h2>
          <button
            onClick={onBack}
            className="rounded-lg bg-meta-4 px-4 py-2 text-white hover:bg-opacity-80"
          >
            Back to Plans
          </button>
        </div>

        <div className="mb-6">
          <h3 className="mb-4 text-xl font-bold text-white">
            You're About to Purchase:
          </h3>
          <div className="mb-6 rounded-lg border border-strokedark bg-boxdark-2 p-6">
            <div className="mb-4 flex items-center justify-between">
              <h4 className="text-lg font-bold text-white">
                {planDetails.name}
              </h4>
              <div>
                <span className="text-2xl font-bold text-primary">
                  ${getPricing()}
                </span>
                <span className="text-bodydark">
                  /{billingInterval === "year" ? "year" : "month"}
                </span>
              </div>
            </div>
            <div className="mb-4">
              <p className="text-bodydark">
                Project Range: {projectRange} projects
              </p>
              <p className="text-bodydark">
                Billing: {billingInterval === "year" ? "Annual" : "Monthly"}
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
          <div className="rounded-lg border border-strokedark bg-boxdark-2 p-6">
            <h3 className="mb-4 text-xl font-bold text-white">Plan Features</h3>
            <ul className="space-y-2">
              {planFeatures[planDetails.name].map((feature, index) => (
                <li key={index} className="flex items-center text-bodydark">
                  <svg
                    className="mr-2 h-5 w-5 text-primary"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  {feature}
                </li>
              ))}
            </ul>
          </div>

          <div className="rounded-lg border border-strokedark bg-boxdark-2 p-6">
            <h3 className="mb-4 text-xl font-bold text-white">
              Available Add-ons
            </h3>
            <p className="mb-4 text-bodydark">
              After completing your subscription, you'll have the option to add
              these features:
            </p>
            <ul className="mb-6 space-y-2">
              {addOnOptions[planDetails.name].map((addon, index) => (
                <li key={index} className="flex items-center text-bodydark">
                  <svg
                    className="mr-2 h-5 w-5 text-primary"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  {addon}
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="mt-8 rounded-lg border border-strokedark bg-boxdark-2 p-6">
          <h3 className="mb-4 text-xl font-bold text-white">
            Important Information
          </h3>
          <div className="space-y-4 text-bodydark">
            <p>
              <span className="font-semibold text-white">Upgrades:</span> You
              can choose to upgrade to another plan or add additional mix
              packages at a later time through your account settings.
            </p>
            <p>
              <span className="font-semibold text-white">Billing:</span> Your
              subscription will be billed{" "}
              {billingInterval === "year" ? "annually" : "monthly"} and will
              automatically renew until canceled.
            </p>
            <p>
              <span className="font-semibold text-white">Cancellation:</span> If
              you cancel your membership, your files will be deleted 1 month
              after cancellation. You'll receive notifications before this
              happens, giving you time to download any important data.
            </p>
            <p>
              <span className="font-semibold text-white">Non-payment:</span> If
              your account goes into non-payment status, your projects and files
              will be preserved for 2 months, during which you'll receive
              reminder emails. After this period, they may be deleted from our
              servers.
            </p>
          </div>
        </div>

        <div className="mt-8 flex justify-center">
          <button
            onClick={handleProceedToPayment}
            className="rounded-full bg-primary px-8 py-3 font-semibold text-white hover:bg-opacity-90"
          >
            Proceed to Payment
          </button>
        </div>
      </div>

      <Elements stripe={stripePromise}>
        <CardDetailsModal
          isOpen={isModalOpen}
          closeModal={() => setIsModalOpen(false)}
          onSubmit={handleCardSubmit}
          loading={isProcessing}
        />
      </Elements>
    </div>
  );
};

export default SubscriptionDetailsPage;
